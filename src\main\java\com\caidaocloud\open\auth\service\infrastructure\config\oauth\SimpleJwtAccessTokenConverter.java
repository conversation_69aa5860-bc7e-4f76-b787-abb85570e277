package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.TokenDto;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.util.Map;

/**
 * 简单对称加密JWT访问令牌转换器
 * 使用HMAC-SHA256算法进行JWT签名和验证
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "jwt.signer.type", havingValue = "simple", matchIfMissing = true)
public class SimpleJwtAccessTokenConverter extends JwtAccessTokenConverter {


    @SneakyThrows
    public SimpleJwtAccessTokenConverter(@Value("${jwt.signer.simple.sign-key}")String signKey,
            @Value("${jwt.signer.simple.verify-key}")String verifyKey){
        ClassPathResource privateKey = new ClassPathResource("private_key.pem");
        ClassPathResource publicKey = new ClassPathResource("public_key.pem");
        setSigningKey(new String(Files.readAllBytes(privateKey.getFile().toPath())));
        setVerifierKey(new String(Files.readAllBytes(publicKey.getFile().toPath())));
    }

    /**
     * 获取签名器类型
     */
    public String getSignerType() {
        return "simple";
    }

    @Override
    public String encode(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        return super.encode(accessToken, authentication);
    }

    @Override
    public Map<String, Object> decode(String token) {
        return super.decode(token);
    }
}
